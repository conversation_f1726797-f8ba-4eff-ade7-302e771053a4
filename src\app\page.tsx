'use client'

import { Theme<PERSON><PERSON>roller, UserProfile, Sidebar } from '@/components'
import { useSidebarStore } from '@/stores'

export default function DashboardPage() {
  const { toggleSidebar } = useSidebarStore()

  return (
    <div className="size-full m-4">
      <Sidebar>
        <div className="flex h-screen min-w-0 flex-col overflow-auto">
          <div
            role="navigation"
            aria-label="Navbar"
            className="flex items-center justify-between px-3 bg-[var(--layout-topbar-background)] z-30 h-16 static duration-300 transition-transform mx-4 mt-4 rounded-box"
          >
            <div className="inline-flex items-center gap-1.5">
              <button
                onClick={toggleSidebar}
                className="btn btn-square btn-ghost"
                aria-label="Toggle sidebar"
                type="button"
              >
                <span className="icon-[solar--hamburger-menu-line-duotone]"></span>
              </button>
              <button className="btn btn-outline btn-sm btn-ghost border-base-300 text-base-content/70 hidden h-9 w-48 justify-start gap-2 text-sm md:flex">
                <span className="icon-[solar--magnifer-outline] size-4"></span>
                <span>Search</span>
              </button>
            </div>
            <div className="inline-flex items-center gap-1.5">
              <ThemeController />
              <UserProfile />
            </div>
          </div>
        </div>
      </Sidebar>
    </div>
  )
}
